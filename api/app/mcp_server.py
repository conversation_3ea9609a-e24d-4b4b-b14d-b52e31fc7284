"""
Refactored MCP Server for OpenMemory - Thin Wrapper Layer

This module implements a thin MCP (Model Context Protocol) server that provides
memory operations for OpenMemory. The server now delegates all business logic
to dedicated service layers while maintaining identical functionality.

Key features:
- Thin wrapper around service layers
- Preserved MCP tool interface
- Clean separation of concerns
- Identical functionality to original server
- Enhanced error handling and type safety
"""

import logging
import json
import datetime
import contextvars
from typing import Optional

from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseS<PERSON>rT<PERSON>sport
from mcp.server.session import ServerSession
from fastapi import FastAPI, Request
from fastapi.routing import APIRouter

# Import service layers
from app.memory_service import MemoryService
from app.health_service import HealthService
from app.mcp_models import (
    AddMemoryRequest, SearchMemoryRequest, ListMemoriesRequest,
    MCPErrorType, create_error_response, create_add_memory_success,
    create_search_memory_success, create_list_memories_success,
    create_health_response, create_metrics_response,
    ProcessedMemory, MemorySearchResult, MemoryListItem
)

####################################################################################
# CRITICAL FIX: Temporary monkeypatch which avoids crashing when a POST message is received
# before a connection has been initialized, e.g: after a deployment.
# This addresses GitHub issue: https://github.com/modelcontextprotocol/python-sdk/issues/423
# pylint: disable-next=protected-access
old__received_request = ServerSession._received_request

async def _received_request(self, *args, **kwargs):
    try:
        return await old__received_request(self, *args, **kwargs)
    except RuntimeError as e:
        if "Received request before initialization was complete" in str(e):
            logging.warning(f"[MCP_FIX] Handled initialization race condition: {e}")
            return None  # Gracefully handle the race condition
        else:
            raise  # Re-raise other RuntimeErrors

# Apply the monkeypatch
# pylint: disable-next=protected-access
ServerSession._received_request = _received_request
logging.info("[MCP_FIX] Applied monkeypatch to handle MCP initialization race condition")
####################################################################################

# Initialize MCP with initialization tracking
mcp = FastMCP("mem0-mcp-server")
_initialization_complete = {}  # Track initialization per session

# Context variables for user_id and client_name
user_id_var: contextvars.ContextVar[str] = contextvars.ContextVar("user_id")
client_name_var: contextvars.ContextVar[str] = contextvars.ContextVar("client_name")

# Create a router for MCP endpoints
mcp_router = APIRouter(prefix="/mcp")

# Initialize service layers
memory_service = MemoryService()
health_service = HealthService()

# Global tracking for concurrent requests
_active_requests = {}
_request_counter = 0


def get_context_variables() -> tuple[Optional[str], Optional[str]]:
    """Get user_id and client_name from context variables."""
    try:
        uid = user_id_var.get(None)
        client_name = client_name_var.get(None)
        return uid, client_name
    except LookupError:
        return None, None


def validate_context() -> tuple[bool, str]:
    """Validate that required context variables are set."""
    uid, client_name = get_context_variables()
    
    if not uid:
        return False, "Error: user_id not provided"
    if not client_name:
        return False, "Error: client_name not provided"
    
    return True, ""


def check_initialization(uid: str, client_name: str) -> bool:
    """Check if the session is properly initialized."""
    session_key = f"{uid}:{client_name}"
    return _initialization_complete.get(session_key, False)


@mcp.tool(description="Add a new memory. This method is called everytime the user informs anything about themselves, their preferences, or anything that has any relevant information which can be useful in the future conversation. This can also be called when the user asks you to remember something.")
async def add_memories(text: str) -> str:
    """Add a new memory to the system with automatic chunking if needed."""
    global _request_counter
    _request_counter += 1
    request_id = _request_counter
    start_time = datetime.datetime.now()
    
    # Get context variables
    uid, client_name = get_context_variables()
    
    # Track this request
    _active_requests[request_id] = {
        'uid': uid,
        'client_name': client_name,
        'start_time': start_time,
        'text_length': len(text) if text else 0
    }
    
    try:
        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error
        
        # Check initialization
        if not check_initialization(uid, client_name):
            return "Error: Memory system is initializing. Please wait a moment and try again."
        
        # Validate request
        try:
            request_data = AddMemoryRequest(text=text)
        except Exception as e:
            error_response = create_error_response(
                MCPErrorType.VALIDATION_ERROR,
                f"Invalid input: {str(e)}"
            )
            return error_response.message
        
        # Call memory service
        success, message, result_data = memory_service.add_memory(
            text=request_data.text,
            user_id=uid,
            client_name=client_name,
            request_id=request_id
        )
        
        if success:
            # Convert result data to response model
            processed_memories = []
            if result_data and result_data.get("processed_memories"):
                for mem_data in result_data["processed_memories"]:
                    processed_memories.append(ProcessedMemory(
                        id=mem_data["id"],
                        event=mem_data["event"],
                        content=mem_data["content"]
                    ))
            
            response = create_add_memory_success(
                message=message,
                processed_memories=processed_memories,
                is_chunked="transaction_id" in (result_data or {}),
                transaction_id=result_data.get("transaction_id") if result_data else None
            )
            return response.message
        else:
            # Determine error type based on message content
            if "paused" in message.lower():
                error_type = MCPErrorType.APP_PAUSED
            elif "unavailable" in message.lower():
                error_type = MCPErrorType.SYSTEM_UNAVAILABLE
            elif "chunk" in message.lower():
                error_type = MCPErrorType.CHUNKING_FAILED
            else:
                error_type = MCPErrorType.OPERATION_FAILED
            
            error_response = create_error_response(error_type, message)
            return error_response.message
    
    except Exception as e:
        logging.exception(f"Error in add_memories: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Internal error: {str(e)}"
        )
        return error_response.message
    
    finally:
        # Clean up request tracking
        if request_id in _active_requests:
            del _active_requests[request_id]


@mcp.tool(description="Search through stored memories. This method is called EVERYTIME the user asks anything.")
async def search_memory(query: str) -> str:
    """Search through stored memories."""
    # Get context variables
    uid, client_name = get_context_variables()
    
    # Validate context
    context_valid, context_error = validate_context()
    if not context_valid:
        return context_error
    
    try:
        # Validate request
        try:
            request_data = SearchMemoryRequest(query=query)
        except Exception as e:
            error_response = create_error_response(
                MCPErrorType.VALIDATION_ERROR,
                f"Invalid search query: {str(e)}"
            )
            return error_response.message
        
        # Call memory service
        success, message, search_results = memory_service.search_memory(
            query=request_data.query,
            user_id=uid,
            client_name=client_name,
            limit=request_data.limit
        )
        
        if success:
            # Convert results to response model
            results = []
            if search_results:
                for result in search_results:
                    # Ensure result is a dictionary
                    if not isinstance(result, dict):
                        logging.warning(f"Skipping non-dict result in search: {type(result)}")
                        continue

                    # Convert UUID to string if needed
                    result_id = result.get("id", "")
                    if hasattr(result_id, '__str__'):
                        result_id = str(result_id)

                    results.append(MemorySearchResult(
                        id=result_id,
                        memory=result.get("memory", ""),
                        score=result.get("score"),
                        metadata=result.get("metadata"),
                        hash=result.get("hash")
                    ))
            
            response = create_search_memory_success(
                message=message,
                results=results,
                query=request_data.query
            )
            
            # Return formatted results
            if results:
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "id": result.id,
                        "memory": result.memory,
                        "score": result.score,
                        "hash": result.hash
                    })
                return json.dumps(formatted_results, indent=2)
            else:
                return message
        else:
            # Determine error type
            if "paused" in message.lower():
                error_type = MCPErrorType.APP_PAUSED
            elif "unavailable" in message.lower():
                error_type = MCPErrorType.SYSTEM_UNAVAILABLE
            else:
                error_type = MCPErrorType.OPERATION_FAILED
            
            error_response = create_error_response(error_type, message)
            return error_response.message
    
    except Exception as e:
        logging.exception(f"Error in search_memory: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Error searching memory: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="List all memories in the user's memory")
async def list_memories() -> str:
    """List all memories for the user."""
    # Get context variables
    uid, client_name = get_context_variables()
    
    # Validate context
    context_valid, context_error = validate_context()
    if not context_valid:
        return context_error
    
    try:
        # Call memory service
        success, message, memories_list = memory_service.list_memories(
            user_id=uid,
            client_name=client_name
        )
        
        if success:
            # Convert results to response model
            memories = []
            if memories_list:
                for memory in memories_list:
                    # Ensure memory is a dictionary
                    if not isinstance(memory, dict):
                        logging.warning(f"Skipping non-dict memory in list: {type(memory)}")
                        continue

                    # Convert all fields to appropriate types
                    memory_id = memory.get("id", "")
                    if hasattr(memory_id, '__str__'):
                        memory_id = str(memory_id)

                    memory_text = memory.get("memory", "")
                    if hasattr(memory_text, '__str__'):
                        memory_text = str(memory_text)

                    memory_hash = memory.get("hash")
                    if memory_hash and hasattr(memory_hash, '__str__'):
                        memory_hash = str(memory_hash)

                    created_at = memory.get("created_at")
                    if created_at and hasattr(created_at, '__str__'):
                        created_at = str(created_at)

                    memories.append(MemoryListItem(
                        id=memory_id,
                        memory=memory_text,
                        metadata=memory.get("metadata"),
                        hash=memory_hash,
                        created_at=created_at
                    ))
            
            response = create_list_memories_success(
                message=message,
                memories=memories
            )
            
            # Return formatted results
            if memories:
                formatted_memories = []
                for memory in memories:
                    formatted_memories.append({
                        "id": memory.id,
                        "memory": memory.memory,
                        "hash": memory.hash
                    })
                return json.dumps(formatted_memories, indent=2)
            else:
                return message
        else:
            # Determine error type
            if "paused" in message.lower():
                error_type = MCPErrorType.APP_PAUSED
            elif "unavailable" in message.lower():
                error_type = MCPErrorType.SYSTEM_UNAVAILABLE
            else:
                error_type = MCPErrorType.OPERATION_FAILED
            
            error_response = create_error_response(error_type, message)
            return error_response.message
    
    except Exception as e:
        logging.exception(f"Error in list_memories: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Error getting memories: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Get system health status and degradation information for the memory system")
async def get_system_health() -> str:
    """Get comprehensive system health status including degradation information."""
    try:
        # Call health service
        success, formatted_message, health_data = health_service.get_system_health()

        if success:
            return formatted_message
        else:
            error_response = create_error_response(
                MCPErrorType.SYSTEM_UNAVAILABLE,
                formatted_message
            )
            return error_response.message

    except Exception as e:
        logging.exception(f"Error in get_system_health: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Error getting system health status: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Emergency cleanup of hung operations and system reset - use when operations are stuck for hours")
async def emergency_system_cleanup() -> str:
    """Emergency cleanup of hung operations and system reset."""
    try:
        # Get context variables
        uid, client_name = get_context_variables()

        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error

        # Import memory client singleton
        from app.utils.memory import MemoryClientSingleton

        # Get singleton instance
        memory_client = MemoryClientSingleton()

        # Perform emergency cleanup
        cleanup_result = memory_client.emergency_cleanup()

        if cleanup_result["success"]:
            actions = "\n".join([f"✅ {action}" for action in cleanup_result["actions_taken"]])
            final_state = cleanup_result["final_state"]

            return f"""🚨 EMERGENCY CLEANUP COMPLETED

Actions Taken:
{actions}

Final System State:
• Queue Size: {final_state['queue_size']}
• Worker Running: {final_state['worker_running']}
• Worker Alive: {final_state['worker_alive']}
• Operation Counter: {final_state['operation_counter']}
• Degraded Mode: {final_state['degraded_mode']}

✅ System has been reset and should be operational."""
        else:
            actions = "\n".join([f"⚠️ {action}" for action in cleanup_result["actions_taken"]])
            return f"""❌ EMERGENCY CLEANUP FAILED

Actions Attempted:
{actions}

Error: {cleanup_result.get('error', 'Unknown error')}

Please check system logs and consider manual intervention."""

    except Exception as e:
        logging.exception(f"Error in emergency_system_cleanup: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Emergency cleanup failed: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Get detailed operation metrics and performance statistics for memory operations")
async def get_operation_metrics() -> str:
    """Get comprehensive operation metrics including timing, success rates, and error analysis."""
    try:
        # Call health service
        success, formatted_message, metrics_data = health_service.get_operation_metrics()
        
        if success:
            return formatted_message
        else:
            error_response = create_error_response(
                MCPErrorType.SYSTEM_UNAVAILABLE,
                formatted_message
            )
            return error_response.message
    
    except Exception as e:
        logging.exception(f"Error in get_operation_metrics: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Error getting operation metrics: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Get evolution intelligence metrics and learning efficiency for the user")
async def get_evolution_metrics(timeframe: str = "week", app_filter: str = None) -> str:
    """Get evolution intelligence metrics and learning efficiency."""
    try:
        # Get context variables
        uid, client_name = get_context_variables()

        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error

        # Import evolution service
        from app.services.evolution_service import evolution_service

        # Get evolution metrics
        metrics = evolution_service.get_evolution_metrics(uid, timeframe, app_filter)

        return metrics

    except Exception as e:
        logging.error(f"Error getting evolution metrics: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Failed to get evolution metrics: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Get personalized learning insights and memory evolution patterns")
async def get_learning_insights(include_categories: bool = True, include_trends: bool = True) -> str:
    """Get personalized learning insights and memory evolution patterns."""
    try:
        # Get context variables
        uid, client_name = get_context_variables()

        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error

        # Import evolution service
        from app.services.evolution_service import evolution_service

        # Get learning insights
        insights = evolution_service.get_learning_insights(uid, include_categories, include_trends)

        return insights

    except Exception as e:
        logging.error(f"Error getting learning insights: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Failed to get learning insights: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Synchronize evolution metrics to fix inconsistencies between different reporting endpoints")
async def sync_evolution_metrics() -> str:
    """Synchronize evolution metrics to ensure consistency across all endpoints."""
    try:
        # Get context variables
        uid, client_name = get_context_variables()

        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error

        # Import evolution service
        from app.services.evolution_service import evolution_service

        # Sync insights from operations
        sync_result = evolution_service.sync_insights_from_operations(uid, days_back=30)

        if sync_result["success"]:
            return f"""✅ EVOLUTION METRICS SYNCHRONIZED

Sync Results:
• Synced Days: {sync_result['synced_days']}
• Total Operations: {sync_result['total_operations']}
• Date Range: {sync_result['date_range']}

✅ All evolution endpoints should now report consistent metrics."""
        else:
            return f"""❌ METRICS SYNC FAILED

Error: {sync_result.get('error', 'Unknown error')}

Please check system logs for more details."""

    except Exception as e:
        logging.exception(f"Error in sync_evolution_metrics: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Metrics sync failed: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Force cleanup of hung operations and get cleanup service status")
async def force_operation_cleanup() -> str:
    """Force immediate cleanup of hung operations."""
    try:
        # Get context variables
        uid, client_name = get_context_variables()

        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error

        # Import cleanup service
        from app.services.operation_cleanup_service import cleanup_service

        # Force cleanup
        cleanup_result = cleanup_service.force_cleanup()

        if cleanup_result["success"]:
            stats = cleanup_result["stats"]
            return f"""✅ OPERATION CLEANUP COMPLETED

Cleanup Results:
• Total Cleanups Performed: {stats['total_cleanups']}
• Hung Operations Found: {stats['hung_operations_found']}
• Operations Terminated: {stats['operations_terminated']}
• Last Cleanup: {stats.get('last_cleanup', 'Never')}

✅ System operations have been cleaned up."""
        else:
            return f"""❌ OPERATION CLEANUP FAILED

Error: {cleanup_result.get('error', 'Unknown error')}

Please check system logs for more details."""

    except Exception as e:
        logging.exception(f"Error in force_operation_cleanup: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Operation cleanup failed: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Get operation cleanup service health and statistics")
async def get_cleanup_service_status() -> str:
    """Get status and statistics for the operation cleanup service."""
    try:
        # Get context variables
        uid, client_name = get_context_variables()

        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error

        # Import cleanup service
        from app.services.operation_cleanup_service import cleanup_service

        # Get service stats
        stats = cleanup_service.get_cleanup_stats()
        health = cleanup_service.check_system_health()

        health_emoji = {
            'healthy': '✅',
            'warning': '⚠️',
            'critical': '🚨',
            'error': '❌'
        }.get(health['health_status'], '❓')

        return f"""📊 OPERATION CLEANUP SERVICE STATUS

Service Configuration:
• Service Running: {stats['service_running']}
• Cleanup Interval: {stats['cleanup_interval_seconds']}s
• Operation Timeout: {stats['operation_timeout_seconds']}s

Cleanup Statistics:
• Total Cleanups: {stats['stats']['total_cleanups']}
• Hung Operations Found: {stats['stats']['hung_operations_found']}
• Operations Terminated: {stats['stats']['operations_terminated']}
• Last Cleanup: {stats['stats'].get('last_cleanup', 'Never')}

System Health: {health_emoji} {health['health_status'].upper()}
• {health['health_message']}
• Total Operations: {health.get('total_operations', 'Unknown')}
• Hung Operations: {health.get('hung_operations', 'Unknown')}
• Recent Operations (1h): {health.get('recent_operations', 'Unknown')}"""

    except Exception as e:
        logging.exception(f"Error in get_cleanup_service_status: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Cleanup service status check failed: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Get comprehensive monitoring dashboard with operation metrics, alerts, and system health")
async def get_monitoring_dashboard() -> str:
    """Get comprehensive monitoring dashboard data."""
    try:
        # Get context variables
        uid, client_name = get_context_variables()

        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error

        # Import cleanup service
        from app.services.operation_cleanup_service import cleanup_service

        # Get dashboard data
        dashboard = cleanup_service.get_monitoring_dashboard()

        if 'error' in dashboard:
            return f"❌ Dashboard Error: {dashboard['error']}"

        # Format the dashboard
        health_emoji = {
            'healthy': '✅',
            'warning': '⚠️',
            'critical': '🚨',
            'error': '❌'
        }.get(dashboard['system_health']['health_status'], '❓')

        recent_alerts_text = ""
        if dashboard['recent_alerts']:
            recent_alerts_text = "\n\nRecent Alerts:\n"
            for alert in dashboard['recent_alerts'][-5:]:  # Last 5 alerts
                alert_emoji = '🚨' if alert['level'] == 'critical' else '⚠️' if alert['level'] == 'warning' else 'ℹ️'
                recent_alerts_text += f"• {alert_emoji} {alert['message']}\n"
        else:
            recent_alerts_text = "\n\n✅ No recent alerts"

        return f"""📊 MEMORY MASTER MONITORING DASHBOARD

🕐 Operation Activity:
• Last Hour: {dashboard['operation_counts']['last_hour']} operations
• Last Day: {dashboard['operation_counts']['last_day']} operations
• Last Week: {dashboard['operation_counts']['last_week']} operations

📈 Operation Types:
• ADD: {dashboard['operation_types']['ADD']}
• UPDATE: {dashboard['operation_types']['UPDATE']}
• DELETE: {dashboard['operation_types']['DELETE']}
• NOOP: {dashboard['operation_types']['NOOP']}

{health_emoji} System Health: {dashboard['system_health']['health_status'].upper()}
• {dashboard['system_health']['health_message']}
• Hung Operations: {dashboard['system_health'].get('hung_operations', 'Unknown')}

🧹 Cleanup Service:
• Total Cleanups: {dashboard['cleanup_stats']['total_cleanups']}
• Operations Terminated: {dashboard['cleanup_stats']['operations_terminated']}
• Alerts Enabled: {dashboard['alert_config']['enabled']}

⚙️ Alert Thresholds:
• Hung Operations Warning: {dashboard['alert_config']['thresholds']['hung_operations_warning']}
• Hung Operations Critical: {dashboard['alert_config']['thresholds']['hung_operations_critical']}
• Operation Rate Warning: {dashboard['alert_config']['thresholds']['operation_rate_warning']}/hour
• Operation Rate Critical: {dashboard['alert_config']['thresholds']['operation_rate_critical']}/hour{recent_alerts_text}

📅 Generated: {dashboard['timestamp']}"""

    except Exception as e:
        logging.exception(f"Error in get_monitoring_dashboard: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Monitoring dashboard failed: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Run comprehensive system validation to test all evolution features and fixes")
async def run_system_validation() -> str:
    """Run comprehensive validation of all system fixes and features."""
    try:
        # Get context variables
        uid, client_name = get_context_variables()

        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error

        validation_results = []
        total_tests = 0
        passed_tests = 0

        # Test 1: Memory Client Health
        total_tests += 1
        try:
            from app.utils.memory import MemoryClientSingleton
            memory_client = MemoryClientSingleton()

            if memory_client._is_client_healthy():
                validation_results.append("✅ Memory Client Health: PASS")
                passed_tests += 1
            else:
                validation_results.append("❌ Memory Client Health: FAIL - Client not healthy")
        except Exception as e:
            validation_results.append(f"❌ Memory Client Health: FAIL - {str(e)}")

        # Test 2: Worker Thread Status
        total_tests += 1
        try:
            from app.utils.memory import MemoryClientSingleton
            memory_client = MemoryClientSingleton()
            queue_status = memory_client.get_operation_queue_status()

            if queue_status["worker_running"] and queue_status["worker_alive"]:
                validation_results.append("✅ Worker Thread Status: PASS")
                passed_tests += 1
            else:
                validation_results.append(f"❌ Worker Thread Status: FAIL - Running: {queue_status['worker_running']}, Alive: {queue_status['worker_alive']}")
        except Exception as e:
            validation_results.append(f"❌ Worker Thread Status: FAIL - {str(e)}")

        # Test 3: Evolution Metrics Consistency
        total_tests += 1
        try:
            from app.services.evolution_service import evolution_service

            # Get metrics from different endpoints
            metrics = evolution_service.get_evolution_metrics(uid, "week")
            insights = evolution_service.get_learning_insights(uid)
            monitor = evolution_service.get_evolution_monitor(uid, limit=5)

            # Check if all return valid data (not error messages)
            if not any("Error" in result for result in [metrics, insights, monitor]):
                validation_results.append("✅ Evolution Metrics Consistency: PASS")
                passed_tests += 1
            else:
                validation_results.append("❌ Evolution Metrics Consistency: FAIL - Some endpoints returning errors")
        except Exception as e:
            validation_results.append(f"❌ Evolution Metrics Consistency: FAIL - {str(e)}")

        # Test 4: Cleanup Service Status
        total_tests += 1
        try:
            from app.services.operation_cleanup_service import cleanup_service

            stats = cleanup_service.get_cleanup_stats()
            health = cleanup_service.check_system_health()

            if stats["service_running"] and health["health_status"] in ["healthy", "warning"]:
                validation_results.append("✅ Cleanup Service Status: PASS")
                passed_tests += 1
            else:
                validation_results.append(f"❌ Cleanup Service Status: FAIL - Service running: {stats['service_running']}, Health: {health['health_status']}")
        except Exception as e:
            validation_results.append(f"❌ Cleanup Service Status: FAIL - {str(e)}")

        # Test 5: Database Connectivity
        total_tests += 1
        try:
            from app.database import SessionLocal
            db = SessionLocal()

            # Simple query to test database
            from app.models import EvolutionOperation
            count = db.query(EvolutionOperation).count()
            db.close()

            validation_results.append(f"✅ Database Connectivity: PASS - {count} operations in database")
            passed_tests += 1
        except Exception as e:
            validation_results.append(f"❌ Database Connectivity: FAIL - {str(e)}")

        # Test 6: Vector Store Connectivity
        total_tests += 1
        try:
            from app.utils.memory import MemoryClientSingleton
            memory_client = MemoryClientSingleton()

            # Test with proper user context
            test_result = memory_client._check_vector_store_direct()

            if test_result:
                validation_results.append("✅ Vector Store Connectivity: PASS")
                passed_tests += 1
            else:
                validation_results.append("❌ Vector Store Connectivity: FAIL - Direct check failed")
        except Exception as e:
            validation_results.append(f"❌ Vector Store Connectivity: FAIL - {str(e)}")

        # Calculate pass rate
        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        # Overall status
        if pass_rate >= 100:
            overall_status = "🎉 EXCELLENT"
        elif pass_rate >= 80:
            overall_status = "✅ GOOD"
        elif pass_rate >= 60:
            overall_status = "⚠️ NEEDS ATTENTION"
        else:
            overall_status = "🚨 CRITICAL"

        results_text = "\n".join(validation_results)

        return f"""🔍 COMPREHENSIVE SYSTEM VALIDATION RESULTS

{results_text}

📊 SUMMARY:
• Tests Passed: {passed_tests}/{total_tests}
• Pass Rate: {pass_rate:.1f}%
• Overall Status: {overall_status}

{'🎯 All systems operational!' if pass_rate >= 100 else '⚠️ Some issues detected - check individual test results above.'}"""

    except Exception as e:
        logging.exception(f"Error in run_system_validation: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"System validation failed: {str(e)}"
        )
        return error_response.message


@mcp.tool(description="Monitor real-time evolution activity and system intelligence status")
async def get_evolution_monitor(limit: int = 10, operation_filter: str = None) -> str:
    """Monitor real-time evolution activity and system intelligence status."""
    try:
        # Get context variables
        uid, client_name = get_context_variables()

        # Validate context
        context_valid, context_error = validate_context()
        if not context_valid:
            return context_error

        # Import evolution service
        from app.services.evolution_service import evolution_service

        # Get evolution monitor data
        monitor_data = evolution_service.get_evolution_monitor(uid, limit, operation_filter)

        return monitor_data

    except Exception as e:
        logging.error(f"Error getting evolution monitor: {e}")
        error_response = create_error_response(
            MCPErrorType.INTERNAL_ERROR,
            f"Failed to get evolution monitor: {str(e)}"
        )
        return error_response.message


# Initialize SSE transport
sse = SseServerTransport("/mcp/messages/")


# SSE Connection Handlers (preserved from original)
from app.database import SessionLocal
from app.utils.db import get_user_and_app


@mcp_router.get("/claude/sse/{user_id}")
async def handle_claude_sse(request: Request):
    """Handle SSE connections for Claude Desktop specifically"""
    # Extract user_id from path parameters
    uid = request.path_params.get("user_id")
    client_name = "claude"  # Fixed client name for Claude Desktop
    session_key = f"{uid}:{client_name}"

    logging.info(f"[MCP_DIAGNOSTIC] Claude Desktop SSE connection initiated for user: {uid}, session: {session_key}")

    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"Pre-registered user '{uid}' and app '{client_name}' for Claude Desktop SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"Failed to pre-register user/app for Claude Desktop: {e}")

    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name)

    try:
        # Handle SSE connection with proper initialization sequence for Claude Desktop
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            logging.info(f"[MCP_DIAGNOSTIC] Starting Claude Desktop initialization for session {session_key}")

            # Create a custom initialization handler with longer delay for Claude Desktop
            class ClaudeInitializationServer:
                def __init__(self, server):
                    self._server = server

                async def run(self, read_stream, write_stream, init_options):
                    # Add a small delay before starting initialization for Claude Desktop
                    import asyncio
                    await asyncio.sleep(0.5)  # 500ms delay for Claude Desktop

                    # CRITICAL FIX: Ensure memory client is initialized before marking complete
                    try:
                        from app.utils.memory import get_memory_client
                        memory_client = get_memory_client()
                        if memory_client is not None:
                            logging.info(f"[MCP_DIAGNOSTIC] Memory client verified for session {session_key}")
                        else:
                            logging.warning(f"[MCP_DIAGNOSTIC] Memory client not ready for session {session_key}")
                    except Exception as e:
                        logging.warning(f"[MCP_DIAGNOSTIC] Memory client check failed for session {session_key}: {e}")

                    # Mark initialization as complete before starting
                    _initialization_complete[session_key] = True
                    logging.info(f"[MCP_DIAGNOSTIC] Claude Desktop initialization marked complete for session {session_key}")

                    # Run the actual server
                    await self._server.run(read_stream, write_stream, init_options)

            aware_server = ClaudeInitializationServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"Claude Desktop SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)


@mcp_router.get("/{client_name}/sse/{user_id}")
async def handle_sse(request: Request):
    """Handle SSE connections for a specific user and client with improved initialization"""
    # Extract user_id and client_name from path parameters
    uid = request.path_params.get("user_id")
    client_name = request.path_params.get("client_name")
    session_key = f"{uid}:{client_name}"

    logging.info(f"[MCP_DIAGNOSTIC] Generic SSE connection initiated for client: {client_name}, user: {uid}, session: {session_key}")

    # Pre-register user and app in database to ensure they exist
    try:
        db = SessionLocal()
        try:
            user, app = get_user_and_app(db, user_id=uid, app_id=client_name)
            logging.info(f"Pre-registered user '{uid}' and app '{client_name}' for SSE connection")
        finally:
            db.close()
    except Exception as e:
        logging.error(f"Failed to pre-register user/app: {e}")

    user_token = user_id_var.set(uid or "")
    client_token = client_name_var.set(client_name or "")

    try:
        # Handle SSE connection with proper initialization sequence
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (read_stream, write_stream):
            # Mark initialization as starting
            _initialization_complete[session_key] = False
            logging.info(f"[MCP_DIAGNOSTIC] Starting initialization for session {session_key}")

            # Create a custom initialization handler
            class InitializationAwareServer:
                def __init__(self, server):
                    self._server = server

                async def run(self, read_stream, write_stream, init_options):
                    # Add a small delay before starting initialization
                    import asyncio
                    await asyncio.sleep(0.2)  # 200ms delay

                    # CRITICAL FIX: Ensure memory client is initialized before marking complete
                    try:
                        from app.utils.memory import get_memory_client
                        memory_client = get_memory_client()
                        if memory_client is not None:
                            logging.info(f"[MCP_DIAGNOSTIC] Memory client verified for session {session_key}")
                        else:
                            logging.warning(f"[MCP_DIAGNOSTIC] Memory client not ready for session {session_key}")
                    except Exception as e:
                        logging.warning(f"[MCP_DIAGNOSTIC] Memory client check failed for session {session_key}: {e}")

                    # Mark initialization as complete before starting
                    _initialization_complete[session_key] = True
                    logging.info(f"[MCP_DIAGNOSTIC] Initialization marked complete for session {session_key}")

                    # Run the actual server
                    await self._server.run(read_stream, write_stream, init_options)

            aware_server = InitializationAwareServer(mcp._mcp_server)
            await aware_server.run(
                read_stream,
                write_stream,
                mcp._mcp_server.create_initialization_options(),
            )
    except Exception as e:
        logging.error(f"SSE connection error for {session_key}: {e}")
        _initialization_complete.pop(session_key, None)
        raise
    finally:
        # Clean up context variables and session tracking
        user_id_var.reset(user_token)
        client_name_var.reset(client_token)
        _initialization_complete.pop(session_key, None)


@mcp_router.post("/messages/")
async def handle_get_message(request: Request):
    """Handle POST messages for SSE - generic endpoint"""
    return await handle_post_message(request)


@mcp_router.post("/{client_name}/sse/{user_id}/messages/")
async def handle_post_message_with_params(request: Request):
    """Handle POST messages for SSE - parameterized endpoint"""
    return await handle_post_message(request)


async def handle_post_message(request: Request):
    """Handle POST messages for SSE"""
    try:
        body = await request.body()

        # Create a simple receive function that returns the body
        async def receive():
            return {"type": "http.request", "body": body, "more_body": False}

        # Create a simple send function that does nothing
        async def send(message):
            return {}

        # Call handle_post_message with the correct arguments
        await sse.handle_post_message(request.scope, receive, send)

        # Return a success response
        return {"status": "ok"}
    except Exception as e:
        logging.error(f"Error handling POST message: {e}")
        return {"status": "error", "message": str(e)}


def setup_mcp_server(app: FastAPI):
    """Setup MCP server with the FastAPI application"""
    mcp._mcp_server.name = f"mem0-mcp-server"

    # Include MCP router in the FastAPI app
    app.include_router(mcp_router)
