"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Plus,
  Trash2,
  Edit,
  Save,
  X,
  Brain,
  Target,
  AlertCircle,
  CheckCircle,
  Download,
  Upload,
  RefreshCw
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface DomainConfig {
  id: string;
  name: string;
  description: string;
  type: "general" | "technical" | "personal" | "business" | "custom";
  isActive: boolean;
  rules: string[];
  priority: number;
  memoryRetention: number;
  conflictResolution: "merge" | "replace" | "ignore";
}

interface DomainTabProps {
  onSettingsChange: (hasChanges: boolean) => void;
}

export function DomainTab({ onSettingsChange }: DomainTabProps) {
  const [domains, setDomains] = useState<DomainConfig[]>([
    {
      id: "1",
      name: "General Knowledge",
      description: "Default domain for general information and facts",
      type: "general",
      isActive: true,
      rules: ["Prioritize recent information", "Merge similar concepts"],
      priority: 1,
      memoryRetention: 90,
      conflictResolution: "merge"
    },
    {
      id: "2", 
      name: "Technical Documentation",
      description: "Code, APIs, and technical specifications",
      type: "technical",
      isActive: true,
      rules: ["Preserve version information", "Link related components"],
      priority: 2,
      memoryRetention: 95,
      conflictResolution: "replace"
    },
    {
      id: "3",
      name: "Personal Preferences",
      description: "User preferences and personalization data",
      type: "personal",
      isActive: false,
      rules: ["Respect privacy settings", "Update preferences incrementally"],
      priority: 3,
      memoryRetention: 85,
      conflictResolution: "merge"
    }
  ]);

  const [editingDomain, setEditingDomain] = useState<string | null>(null);
  const [newRule, setNewRule] = useState("");
  const [showAddDomain, setShowAddDomain] = useState(false);
  const [switchingDomain, setSwitchingDomain] = useState<string | null>(null);
  const [backupInProgress, setBackupInProgress] = useState(false);
  const [newDomain, setNewDomain] = useState<Partial<DomainConfig>>({
    name: "",
    description: "",
    type: "custom",
    isActive: true,
    rules: [],
    priority: domains.length + 1,
    memoryRetention: 80,
    conflictResolution: "merge"
  });

  const handleDomainUpdate = (domainId: string, updates: Partial<DomainConfig>) => {
    setDomains(prev => prev.map(domain => 
      domain.id === domainId ? { ...domain, ...updates } : domain
    ));
    onSettingsChange(true);
  };

  const handleAddRule = (domainId: string) => {
    if (newRule.trim()) {
      const domain = domains.find(d => d.id === domainId);
      if (domain) {
        handleDomainUpdate(domainId, {
          rules: [...domain.rules, newRule.trim()]
        });
        setNewRule("");
      }
    }
  };

  const handleRemoveRule = (domainId: string, ruleIndex: number) => {
    const domain = domains.find(d => d.id === domainId);
    if (domain) {
      handleDomainUpdate(domainId, {
        rules: domain.rules.filter((_, index) => index !== ruleIndex)
      });
    }
  };

  const handleAddDomain = () => {
    if (newDomain.name && newDomain.description) {
      const domain: DomainConfig = {
        id: Date.now().toString(),
        name: newDomain.name,
        description: newDomain.description,
        type: newDomain.type || "custom",
        isActive: newDomain.isActive || true,
        rules: newDomain.rules || [],
        priority: newDomain.priority || domains.length + 1,
        memoryRetention: newDomain.memoryRetention || 80,
        conflictResolution: newDomain.conflictResolution || "merge"
      };
      
      setDomains(prev => [...prev, domain]);
      setNewDomain({
        name: "",
        description: "",
        type: "custom",
        isActive: true,
        rules: [],
        priority: domains.length + 2,
        memoryRetention: 80,
        conflictResolution: "merge"
      });
      setShowAddDomain(false);
      onSettingsChange(true);
    }
  };

  const handleDeleteDomain = (domainId: string) => {
    if (confirm("Are you sure you want to delete this domain? This action cannot be undone.")) {
      setDomains(prev => prev.filter(domain => domain.id !== domainId));
      onSettingsChange(true);
    }
  };

  const handleDomainSwitch = async (domainType: "technical" | "business") => {
    setSwitchingDomain(domainType);
    setBackupInProgress(true);

    try {
      // Call the backend API to switch domain
      const response = await fetch(`http://localhost:8765/api/v1/evolution-config/domain/switch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain_type: domainType }),
      });

      if (!response.ok) {
        throw new Error(`Failed to switch domain: ${response.statusText}`);
      }

      const result = await response.json();
      console.log(`Successfully switched to ${domainType} domain:`, result);

      // Update domain configurations in UI
      if (domainType === "technical") {
        // Apply technical domain settings
        setDomains(prev => prev.map(domain => ({
          ...domain,
          isActive: domain.type === "technical"
        })));
      } else {
        // Apply business domain settings
        setDomains(prev => prev.map(domain => ({
          ...domain,
          isActive: domain.type === "business"
        })));
      }

      onSettingsChange(true);

      // Show success message
      alert(`Successfully switched to ${domainType} domain. Custom prompts are now ${result.custom_prompts_enabled ? 'enabled' : 'disabled'}.`);

    } catch (error) {
      console.error("Domain switch failed:", error);
      alert(`Failed to switch domain: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setBackupInProgress(false);
      setSwitchingDomain(null);
    }
  };

  const createBackup = async () => {
    setBackupInProgress(true);
    try {
      // Simulate backup creation
      await new Promise(resolve => setTimeout(resolve, 1500));

      const backup = {
        timestamp: new Date().toISOString(),
        domains: domains,
        version: "1.0.0"
      };

      const dataStr = JSON.stringify(backup, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', `domain-backup-${new Date().toISOString().split('T')[0]}.json`);
      linkElement.click();
    } finally {
      setBackupInProgress(false);
    }
  };

  const getDomainTypeColor = (type: string) => {
    switch (type) {
      case "general": return "bg-blue-900/20 text-blue-400 border-blue-800";
      case "technical": return "bg-green-900/20 text-green-400 border-green-800";
      case "personal": return "bg-purple-900/20 text-purple-400 border-purple-800";
      case "business": return "bg-orange-900/20 text-orange-400 border-orange-800";
      default: return "bg-zinc-900/20 text-zinc-400 border-zinc-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Domain Explanation */}
      <Card className="bg-blue-900/10 border-blue-800/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-400">
            <Brain className="h-5 w-5" />
            Understanding Domain Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-zinc-200 mb-2">Domain Types (Top Section)</h4>
              <ul className="space-y-1 text-zinc-400">
                <li>• <strong className="text-blue-400">Technical Development:</strong> Optimized for code, APIs, documentation</li>
                <li>• <strong className="text-orange-400">Business Operations:</strong> Focused on business processes and decisions</li>
                <li>• These are <strong>domain presets</strong> that configure the entire system</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-zinc-200 mb-2">Memory Configurations (Bottom Section)</h4>
              <ul className="space-y-1 text-zinc-400">
                <li>• <strong className="text-green-400">Individual memory domains:</strong> General Knowledge, Technical Documentation, etc.</li>
                <li>• Each has its own <strong>processing rules</strong> and settings</li>
                <li>• "Add Domain" creates new memory configuration rules</li>
              </ul>
            </div>
          </div>
          <div className="p-3 bg-zinc-900/50 rounded-lg border border-zinc-800">
            <p className="text-sm text-zinc-300">
              <strong>Current Setup:</strong> You can switch between <span className="text-blue-400">Technical</span> or <span className="text-orange-400">Business</span> domain presets (top),
              while managing individual memory configurations (bottom) that define how specific types of content are processed.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Domain Presets Section */}
      <div>
        <h3 className="text-lg font-semibold">Domain Presets</h3>
        <p className="text-sm text-zinc-400">
          Choose your primary domain configuration preset
        </p>
      </div>

      {/* Quick Domain Selection Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="bg-gradient-to-br from-blue-900/20 to-blue-800/10 border-blue-800/50 cursor-pointer hover:border-blue-700 transition-colors">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-400">
              <Brain className="h-5 w-5" />
              Technical Development
            </CardTitle>
            <CardDescription className="text-blue-300/70">
              Optimized for code, APIs, documentation, and technical specifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-400" />
                <span>Version-aware memory management</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-400" />
                <span>Code context preservation</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-400" />
                <span>API relationship mapping</span>
              </div>
            </div>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  className="w-full mt-4 bg-blue-600 hover:bg-blue-700"
                  disabled={backupInProgress || switchingDomain === "technical"}
                >
                  {switchingDomain === "technical" ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Switching...
                    </>
                  ) : (
                    "Switch to Technical Domain"
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Switch to Technical Development Domain?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will optimize memory evolution for technical content including code, APIs, and documentation.
                    A backup of your current configuration will be created automatically.
                    <br /><br />
                    <strong>Impact:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Memory processing will prioritize technical context</li>
                      <li>Version information will be preserved</li>
                      <li>Code relationships will be mapped automatically</li>
                    </ul>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDomainSwitch("technical")}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Switch Domain
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-900/20 to-orange-800/10 border-orange-800/50 cursor-pointer hover:border-orange-700 transition-colors">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-400">
              <Target className="h-5 w-5" />
              Business Operations
            </CardTitle>
            <CardDescription className="text-orange-300/70">
              Focused on business processes, decisions, and operational knowledge
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-orange-400" />
                <span>Decision context tracking</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-orange-400" />
                <span>Process optimization</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-orange-400" />
                <span>Stakeholder relationship mapping</span>
              </div>
            </div>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  className="w-full mt-4 bg-orange-600 hover:bg-orange-700"
                  disabled={backupInProgress || switchingDomain === "business"}
                >
                  {switchingDomain === "business" ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Switching...
                    </>
                  ) : (
                    "Switch to Business Domain"
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Switch to Business Operations Domain?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will optimize memory evolution for business processes, decisions, and operational knowledge.
                    A backup of your current configuration will be created automatically.
                    <br /><br />
                    <strong>Impact:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Decision context will be tracked and preserved</li>
                      <li>Process optimization patterns will be identified</li>
                      <li>Stakeholder relationships will be mapped</li>
                    </ul>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDomainSwitch("business")}
                    className="bg-orange-600 hover:bg-orange-700"
                  >
                    Switch Domain
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </CardContent>
        </Card>
      </div>

      {/* Backup Management */}
      <Card className="bg-zinc-900/50 border-zinc-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Backup & Restore
          </CardTitle>
          <CardDescription>
            Manage domain configuration backups and restore points
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={createBackup}
              disabled={backupInProgress}
              variant="outline"
              className="border-zinc-700"
            >
              {backupInProgress ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Creating Backup...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Create Backup
                </>
              )}
            </Button>

            <Button
              variant="outline"
              className="border-zinc-700"
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                input.onchange = (e) => {
                  const file = (e.target as HTMLInputElement).files?.[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                      try {
                        const backup = JSON.parse(e.target?.result as string);
                        if (confirm("This will restore domain configuration from backup. Current settings will be lost. Continue?")) {
                          setDomains(backup.domains || []);
                          onSettingsChange(true);
                        }
                      } catch (error) {
                        alert('Invalid backup file format');
                      }
                    };
                    reader.readAsText(file);
                  }
                };
                input.click();
              }}
            >
              <Upload className="mr-2 h-4 w-4" />
              Restore Backup
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Add New Domain Form */}
      {showAddDomain && (
        <Card className="bg-zinc-900/50 border-zinc-800 border-dashed">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Add New Domain
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAddDomain(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="new-domain-name">Domain Name</Label>
                <Input
                  id="new-domain-name"
                  value={newDomain.name || ""}
                  onChange={(e) => setNewDomain(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter domain name"
                  className="bg-zinc-800 border-zinc-700"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="new-domain-type">Domain Type</Label>
                <Select
                  value={newDomain.type}
                  onValueChange={(value) => setNewDomain(prev => ({ ...prev, type: value as any }))}
                >
                  <SelectTrigger className="bg-zinc-800 border-zinc-700">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                    <SelectItem value="personal">Personal</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="new-domain-description">Description</Label>
              <Textarea
                id="new-domain-description"
                value={newDomain.description || ""}
                onChange={(e) => setNewDomain(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe the purpose and scope of this domain"
                className="bg-zinc-800 border-zinc-700"
                rows={3}
              />
            </div>
            
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowAddDomain(false)}
                className="border-zinc-700"
              >
                Cancel
              </Button>
              <Button onClick={handleAddDomain}>
                <Save className="mr-2 h-4 w-4" />
                Add Domain
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Memory Configuration Rules */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">Memory Configuration Rules</h3>
            <p className="text-sm text-zinc-400">
              Individual memory domains with specific processing rules and settings
            </p>
          </div>
          <Button
            onClick={() => setShowAddDomain(true)}
            className="bg-primary hover:bg-primary/90"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Domain
          </Button>
        </div>

        {domains.map((domain) => (
          <Card key={domain.id} className="bg-zinc-900/50 border-zinc-800">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Brain className="h-5 w-5" />
                    <CardTitle className="text-lg">{domain.name}</CardTitle>
                  </div>
                  <Badge className={getDomainTypeColor(domain.type)}>
                    {domain.type}
                  </Badge>
                  {domain.isActive ? (
                    <Badge className="bg-green-900/20 text-green-400 border-green-800">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                  ) : (
                    <Badge variant="secondary">
                      <AlertCircle className="mr-1 h-3 w-3" />
                      Inactive
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  <Switch
                    checked={domain.isActive}
                    onCheckedChange={(checked) => handleDomainUpdate(domain.id, { isActive: checked })}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setEditingDomain(editingDomain === domain.id ? null : domain.id)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteDomain(domain.id)}
                    className="text-red-400 hover:text-red-300"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>{domain.description}</CardDescription>
            </CardHeader>
            
            {editingDomain === domain.id && (
              <CardContent className="space-y-4 border-t border-zinc-800 pt-4">
                {/* Domain Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Priority</Label>
                    <Input
                      type="number"
                      value={domain.priority}
                      onChange={(e) => handleDomainUpdate(domain.id, { priority: parseInt(e.target.value) })}
                      className="bg-zinc-800 border-zinc-700"
                      min="1"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Memory Retention (%)</Label>
                    <Input
                      type="number"
                      value={domain.memoryRetention}
                      onChange={(e) => handleDomainUpdate(domain.id, { memoryRetention: parseInt(e.target.value) })}
                      className="bg-zinc-800 border-zinc-700"
                      min="0"
                      max="100"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Conflict Resolution</Label>
                    <Select
                      value={domain.conflictResolution}
                      onValueChange={(value) => handleDomainUpdate(domain.id, { conflictResolution: value as any })}
                    >
                      <SelectTrigger className="bg-zinc-800 border-zinc-700">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="merge">Merge</SelectItem>
                        <SelectItem value="replace">Replace</SelectItem>
                        <SelectItem value="ignore">Ignore</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                {/* Domain Rules */}
                <div className="space-y-3">
                  <Label>Domain Rules</Label>
                  <div className="space-y-2">
                    {domain.rules.map((rule, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-zinc-800/50 rounded">
                        <Target className="h-4 w-4 text-zinc-400" />
                        <span className="flex-1 text-sm">{rule}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveRule(domain.id, index)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                    
                    <div className="flex gap-2">
                      <Input
                        value={newRule}
                        onChange={(e) => setNewRule(e.target.value)}
                        placeholder="Add new rule..."
                        className="bg-zinc-800 border-zinc-700"
                        onKeyPress={(e) => e.key === 'Enter' && handleAddRule(domain.id)}
                      />
                      <Button
                        onClick={() => handleAddRule(domain.id)}
                        disabled={!newRule.trim()}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>
    </div>
  );
}
