# Memory Types Classification - Product Requirements Document

## 1. Executive Summary

### 1.1 Overview
Memory Types Classification is a critical enhancement to Memory Master v2 that introduces intelligent categorization of memories based on human memory patterns. This feature will implement four distinct memory types: Working, Factual, Episodic, and Semantic memory, enabling more sophisticated memory organization, retrieval, and management.

### 1.2 Business Justification
- **Competitive Advantage**: Brings Memory Master v2 closer to Mem0's capabilities while maintaining our privacy-first approach
- **User Experience**: Improves memory relevance and retrieval accuracy through intelligent categorization
- **Performance**: Enables optimized memory retrieval strategies based on memory type
- **Scalability**: Provides foundation for advanced memory management features

### 1.3 Success Metrics
- Memory retrieval accuracy improvement: >25%
- User satisfaction with memory relevance: >90%
- Memory categorization accuracy: >85%
- API response time maintained: <200ms

## 2. Product Vision & Goals

### 2.1 Vision Statement
To create an intelligent memory classification system that mimics human memory patterns, enabling AI agents to store, organize, and retrieve memories with human-like efficiency and relevance.

### 2.2 Primary Goals
1. **Intelligent Classification**: Automatically categorize memories into appropriate types
2. **Enhanced Retrieval**: Improve memory search and retrieval based on type-specific strategies
3. **User Control**: Provide manual override capabilities for memory type assignment
4. **Backward Compatibility**: Ensure existing memories continue to function seamlessly
5. **Performance Optimization**: Maintain or improve current system performance

### 2.3 Non-Goals
- Complete rewrite of existing memory storage system
- Breaking changes to current API endpoints
- Removal of existing memory management features

## 3. Memory Types Specification

### 3.1 Working Memory
**Purpose**: Short-term, session-aware memory for immediate context

**Characteristics**:
- Temporary storage (session-based)
- High access frequency
- Automatic cleanup after session ends
- Limited capacity (configurable, default: 50 items)

**Examples**:
- Current conversation context
- Active task variables
- Temporary user preferences
- Session-specific configurations

**Storage Strategy**: In-memory cache with database backup
**TTL**: Session duration + 1 hour
**Priority**: Highest for current session

### 3.2 Factual Memory
**Purpose**: Long-term structured knowledge and preferences

**Characteristics**:
- Persistent storage
- Structured data format
- High confidence information
- Infrequent updates

**Examples**:
- User preferences and settings
- Personal information (name, role, etc.)
- System configurations
- Established facts about users/applications

**Storage Strategy**: Primary database with vector embeddings
**TTL**: No expiration (manual cleanup only)
**Priority**: High for user-specific queries

### 3.3 Episodic Memory
**Purpose**: Specific past conversations and interactions

**Characteristics**:
- Time-stamped events
- Contextual information
- Narrative structure
- Searchable by time/context

**Examples**:
- Previous conversations
- Specific problem-solving sessions
- User feedback and interactions
- Historical decision points

**Storage Strategy**: Database with temporal indexing
**TTL**: Configurable (default: 1 year)
**Priority**: Medium for historical context

### 3.4 Semantic Memory
**Purpose**: General knowledge and learned patterns

**Characteristics**:
- Abstract concepts and patterns
- Cross-session learning
- Generalized knowledge
- Evolving understanding

**Examples**:
- Learned user behavior patterns
- General domain knowledge
- Common problem-solution mappings
- Abstracted insights from multiple interactions

**Storage Strategy**: Vector database with concept clustering
**TTL**: Long-term (default: 2 years)
**Priority**: Medium for pattern-based queries

## 4. Technical Requirements

### 4.1 Database Schema Changes

#### 4.1.1 Memory Types Table
```sql
CREATE TABLE memory_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    default_ttl INTERVAL,
    max_capacity INTEGER,
    storage_strategy VARCHAR(50),
    priority_weight INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 4.1.2 Memories Table Updates
```sql
ALTER TABLE memories ADD COLUMN memory_type_id INTEGER REFERENCES memory_types(id);
ALTER TABLE memories ADD COLUMN confidence_score FLOAT DEFAULT 0.8;
ALTER TABLE memories ADD COLUMN auto_classified BOOLEAN DEFAULT TRUE;
ALTER TABLE memories ADD COLUMN expires_at TIMESTAMP;
ALTER TABLE memories ADD COLUMN access_frequency INTEGER DEFAULT 0;
ALTER TABLE memories ADD COLUMN last_accessed_at TIMESTAMP;
```

#### 4.1.3 Memory Classification Log
```sql
CREATE TABLE memory_classification_log (
    id SERIAL PRIMARY KEY,
    memory_id INTEGER REFERENCES memories(id),
    original_type_id INTEGER REFERENCES memory_types(id),
    new_type_id INTEGER REFERENCES memory_types(id),
    classification_method VARCHAR(50), -- 'auto', 'manual', 'ml_model'
    confidence_score FLOAT,
    reason TEXT,
    classified_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 4.2 API Enhancements

#### 4.2.1 New Endpoints
```
GET /api/memory-types
GET /api/memory-types/{id}
POST /api/memories/{id}/classify
PUT /api/memories/{id}/memory-type
GET /api/memories/by-type/{type_name}
```

#### 4.2.2 Enhanced Existing Endpoints
- `POST /api/memories`: Add optional `memory_type` parameter
- `GET /api/memories`: Add `memory_type` filter parameter
- `GET /api/memories/{id}`: Include memory type information

### 4.3 Memory Classification Engine

#### 4.3.1 Rule-Based Classifier
**Priority**: High (Phase 1 implementation)

**Rules**:
- **Working Memory**: Session-scoped, temporary keywords, current context indicators
- **Factual Memory**: Preference keywords, personal info patterns, configuration data
- **Episodic Memory**: Conversation markers, temporal references, narrative structure
- **Semantic Memory**: Abstract concepts, pattern indicators, generalized knowledge

#### 4.3.2 ML-Based Classifier (Future Enhancement)
**Priority**: Medium (Phase 2)

**Features**:
- Text classification model
- Context analysis
- User behavior patterns
- Confidence scoring

### 4.4 Memory Retrieval Strategy

#### 4.4.1 Type-Aware Search
```python
class TypeAwareMemoryRetrieval:
    def search(self, query: str, memory_types: List[str] = None, 
              context: str = None) -> List[Memory]:
        # Implement type-specific search strategies
        pass
    
    def get_working_memory(self, session_id: str) -> List[Memory]:
        # Fast in-memory retrieval
        pass
    
    def get_factual_memory(self, user_id: str, category: str) -> List[Memory]:
        # Structured data retrieval
        pass
```

#### 4.4.2 Priority-Based Ranking
- Working Memory: Highest priority for current session
- Factual Memory: High priority for user-specific queries
- Episodic Memory: Medium priority, time-weighted
- Semantic Memory: Medium priority, relevance-weighted

## 5. User Experience Requirements

### 5.1 Web Dashboard Enhancements

#### 5.1.1 Memory Type Visualization
- Color-coded memory cards by type
- Memory type filter in sidebar
- Type distribution charts
- Memory type statistics

#### 5.1.2 Manual Classification Interface
- Memory type dropdown in memory details
- Bulk classification tools
- Classification confidence indicators
- Override auto-classification option

#### 5.1.3 Type-Specific Views
- Working Memory: Session-based timeline view
- Factual Memory: Structured data table
- Episodic Memory: Chronological conversation view
- Semantic Memory: Concept map visualization

### 5.2 MCP Server Integration

#### 5.2.1 Enhanced MCP Tools
```
add_memories: Add memory_type parameter
search_memory: Add memory_type filter
list_memories: Add type-based filtering
get_memory_types: New tool for type information
classify_memory: New tool for manual classification
```

#### 5.2.2 Backward Compatibility
- Existing MCP tools continue to work
- Default memory type assignment for unspecified types
- Graceful handling of legacy memories

## 6. Implementation Plan

### 6.1 Phase 1: Foundation (Weeks 1-2)
**Deliverables**:
- Database schema updates
- Memory types seeding
- Basic classification rules
- API endpoint updates

**Tasks**:
1. Create database migration scripts
2. Implement memory types CRUD operations
3. Update memory model and schemas
4. Create rule-based classifier
5. Update API endpoints

### 6.2 Phase 2: Core Features (Weeks 3-4)
**Deliverables**:
- Type-aware memory retrieval
- Web dashboard updates
- MCP server enhancements
- Basic testing

**Tasks**:
1. Implement type-aware search algorithms
2. Update web dashboard components
3. Enhance MCP server tools
4. Create migration script for existing memories
5. Implement memory lifecycle management

### 6.3 Phase 3: Advanced Features (Weeks 5-6)
**Deliverables**:
- Advanced UI features
- Performance optimizations
- Comprehensive testing
- Documentation

**Tasks**:
1. Implement type-specific visualizations
2. Add bulk classification tools
3. Performance testing and optimization
4. Comprehensive test suite
5. User documentation

### 6.4 Phase 4: Polish & Launch (Week 7)
**Deliverables**:
- Production deployment
- Monitoring setup
- User training materials

**Tasks**:
1. Production deployment scripts
2. Monitoring and alerting setup
3. User guide creation
4. Performance baseline establishment

## 7. Technical Specifications

### 7.1 Performance Requirements
- Memory classification: <100ms per memory
- Type-aware search: <200ms response time
- Dashboard loading: <2 seconds
- Memory type switching: <500ms

### 7.2 Scalability Requirements
- Support for 1M+ memories per user
- Concurrent classification of 100+ memories
- Real-time type updates
- Efficient memory type indexing

### 7.3 Security Requirements
- Memory type access control
- Audit logging for type changes
- Data privacy compliance
- Secure API endpoints

## 8. Testing Strategy

### 8.1 Unit Testing
- Memory type CRUD operations
- Classification algorithm accuracy
- API endpoint functionality
- Database migration scripts

### 8.2 Integration Testing
- End-to-end memory lifecycle
- MCP server tool integration
- Web dashboard functionality
- Cross-component data flow

### 8.3 Performance Testing
- Memory classification speed
- Search performance by type
- Dashboard responsiveness
- Concurrent user scenarios

### 8.4 User Acceptance Testing
- Memory type accuracy validation
- UI/UX usability testing
- Workflow efficiency testing
- Feature completeness verification

## 9. Risk Assessment

### 9.1 Technical Risks
**Risk**: Database migration complexity
**Mitigation**: Comprehensive testing, rollback procedures

**Risk**: Performance degradation
**Mitigation**: Performance monitoring, optimization strategies

**Risk**: Classification accuracy
**Mitigation**: Rule refinement, user feedback integration

### 9.2 Business Risks
**Risk**: User adoption resistance
**Mitigation**: Gradual rollout, user training, backward compatibility

**Risk**: Development timeline delays
**Mitigation**: Agile methodology, regular checkpoints, scope flexibility

## 10. Success Criteria

### 10.1 Technical Success
- ✅ All memory types implemented and functional
- ✅ Classification accuracy >85%
- ✅ Performance requirements met
- ✅ Zero data loss during migration
- ✅ Backward compatibility maintained

### 10.2 User Success
- ✅ User satisfaction score >4.5/5
- ✅ Memory retrieval relevance improved >25%
- ✅ Feature adoption rate >70% within 30 days
- ✅ Support ticket reduction for memory-related issues

### 10.3 Business Success
- ✅ Competitive feature parity with Mem0
- ✅ Enhanced product differentiation
- ✅ Foundation for advanced memory features
- ✅ Positive user feedback and testimonials

## 11. Future Enhancements

### 11.1 Machine Learning Integration
- Advanced classification models
- Personalized memory type preferences
- Automatic memory importance scoring
- Predictive memory retrieval

### 11.2 Advanced Memory Management
- Memory type-specific retention policies
- Intelligent memory archiving
- Cross-type memory relationships
- Memory evolution tracking

### 11.3 Analytics and Insights
- Memory type usage analytics
- Classification accuracy metrics
- User behavior pattern analysis
- Memory effectiveness reporting

## 12. Appendices

### 12.1 Memory Type Examples

#### Working Memory Examples
```
- "Currently working on user authentication feature"
- "Debug session for API timeout issue"
- "Temporary API key for testing: temp_key_123"
- "User prefers dark mode for this session"
```

#### Factual Memory Examples
```
- "User John Smith prefers email notifications"
- "Application 'TaskManager' uses PostgreSQL database"
- "User's timezone is UTC-8 (Pacific)"
- "Default theme setting: dark mode"
```

#### Episodic Memory Examples
```
- "On 2024-01-15, user reported login issues with OAuth"
- "Previous conversation about implementing Redis caching"
- "User feedback session on 2024-01-10 about UI improvements"
- "Debugging session for memory leak in API service"
```

#### Semantic Memory Examples
```
- "Users typically prefer faster response times over feature richness"
- "Authentication issues often relate to token expiration"
- "Database performance problems usually occur during peak hours"
- "UI feedback generally focuses on usability improvements"
```

### 12.2 Classification Rules Reference

#### Working Memory Indicators
- Keywords: "currently", "now", "today", "this session", "temporary"
- Context: Session-scoped data, immediate tasks, active debugging
- Patterns: Time-sensitive information, current state references

#### Factual Memory Indicators
- Keywords: "prefers", "always", "never", "setting", "configuration"
- Context: User preferences, system settings, established facts
- Patterns: Structured data, persistent preferences, user profiles

#### Episodic Memory Indicators
- Keywords: "yesterday", "last week", "conversation", "discussed", "mentioned"
- Context: Past interactions, historical events, specific incidents
- Patterns: Temporal references, narrative structure, event descriptions

#### Semantic Memory Indicators
- Keywords: "usually", "typically", "often", "generally", "pattern"
- Context: General knowledge, learned behaviors, abstract concepts
- Patterns: Generalized statements, pattern descriptions, conceptual knowledge

---

**Document Version**: 1.0  
**Last Updated**: 2024-01-20  
**Author**: Memory Master v2 Development Team  
**Status**: Draft for Review